import mongoose, { Document, Schema, Model } from "mongoose";
import { ENUM_GENDER, ENUM_RELATION } from "../../common/enums/enums";

// Define interfaces for embedded documents
interface IAddress {
  addressLine1?: string;
  addressLine2?: string;
  postalCode?: number;
  city?: mongoose.Types.ObjectId;
  state?: mongoose.Types.ObjectId;
  country?: string;
  gstNumber?: string;
}

interface IBusinessAddress {
  businessName?: string;
  addressLine1?: string;
  addressLine2?: string;
  postalCode?: number;
  city?: mongoose.Types.ObjectId;
  state?: mongoose.Types.ObjectId;
  country?: string;
  gstNumber?: string;
}

interface IPolicy {
  policyType: string;
  documentUrl: string;
  isEnabled: boolean;
}

// Main client interface
export interface IClient extends Document {
  createdBy: mongoose.Types.ObjectId;
  organizationId: mongoose.Types.ObjectId;
  facilityId?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  membershipId?: string;
  clientId?: string;
  dob: Date;
  gender: string;
  activityLevel?: string;
  address?: IAddress;
  businessAddress?: IBusinessAddress;
  isBusiness: boolean;
  emergencyContactPerson?: string;
  emergencyContactPhone?: string;
  photo?: string;
  policies?: IPolicy[];
  basicAssessments?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Create schemas for embedded documents
const AddressSchema = new Schema({
  addressLine1: { type: String },
  addressLine2: { type: String },
  postalCode: { type: Number },
  city: { type: Schema.Types.ObjectId, ref: "Cities" },
  state: { type: Schema.Types.ObjectId, ref: "State" },
  country: { type: String }
});

const BusinessAddressSchema = new Schema({
  businessName: { type: String },
  addressLine1: { type: String },
  addressLine2: { type: String },
  postalCode: { type: Number },
  city: { type: Schema.Types.ObjectId, ref: "Cities" },
  state: { type: Schema.Types.ObjectId, ref: "State" },
  country: { type: String },
  gstNumber: { type: String }
});

// Main client schema
const ClientSchema = new Schema({
  createdBy: {
    type: Schema.Types.ObjectId,
    required: true,
    index: true,
    ref: "User"
  },
  organizationId: {
    type: Schema.Types.ObjectId,
    required: true
  },
  facilityId: {
    type: Schema.Types.ObjectId,
    index: true,
    ref: "Facility",
    required: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    required: true,
    index: true,
    ref: "User"
  },
  membershipId: {
    type: String
  },
  clientId: {
    type: String,
    required: true,
    index: true,
  },
  dob: {
    type: Date,
    required: false,
    default: null
  },

  gender: {
    type: String,
    enum: Object.values(ENUM_GENDER),
    lowercase: true,
    trim: true,
    required: false,
    default: ""
  },
  activityLevel: {
    type: String
  },
  address: {
    type: AddressSchema,
    default: null
  },
  businessAddress: {
    type: BusinessAddressSchema,
    default: null,
    validate: {
      validator: function (this: any, value: any) {
        return this.isBusiness ? !!value : true;
      },
      message: "Business address is required when isBusiness is true."
    }
  },
  isBusiness: {
    type: Boolean,
    default: false
  },
  emergencyContactPerson: {
    type: String
  },
  emergencyContactPhone: {
    type: String
  },
  photo: {
    type: String,
    default: ""
  },
  policies: [{
    policyType: String,
    documentUrl: { type: String, default: "" },
    isEnabled: Boolean
  }],
  basicAssessments: {
    type: Object,
    default: {}
  },
  relation: {
    type: String,
    enum: Object.values(ENUM_RELATION),
    lowercase: true,
    trim: true,
    required: false,
    default: undefined
  }
}, {
  timestamps: true
});

export const CLIENT_COLLECTION = 'clients';
export const Client = mongoose.model<IClient>('Client', ClientSchema, CLIENT_COLLECTION);

ClientSchema.index({ clientId: 1, organizationId: 1 }, { unique: true });