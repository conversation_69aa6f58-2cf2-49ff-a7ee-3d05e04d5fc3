import * as path from 'path';
import * as fs from 'fs';
import uuid4 from "uuid4";
import mime from "mime";
import { connectToMongo, closeConnection } from '../../common/database/db.module';
import { parseCSVWithRowNumbers } from '../../common/utils/csv-parser';
import { DocumentLocker, IDocumentLocker } from './document-locker.model';
import { User } from '../user/user.model';
import { Client } from '../user/client.model';
import { LoggerConfig } from '../../common/logger/log.module';
import { ENUM_ROLE_TYPE } from '../role/role.enum';
import { S3Service } from '../../common/aws/s3.service';

interface ICsvDocumentLocker {
  file_id: string;
  id: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  dob: string;
  email: string;
  phone: string;
  path: string;
  waiver_date: string;
}

interface MigrationStats {
  totalRecordsInCSV: number;
  validRecordsAfterValidation: number;
  totalUsersInCSV: number;
  usersFoundInDatabase: number;
  usersWithClientRecords: number;
  filesFoundOnDisk: number;
  documentsReadyForProcessing: number;
  s3UploadsAttempted: number;
  s3UploadsSuccessful: number;
  s3UploadsFailed: number;
  mongoSaveAttempted: number;
  mongoSaveSuccessful: number;
  mongoSaveFailed: number;
  totalSkipped: number;
  validationErrors: number;
}

interface ProcessedRecord {
  row_number: number;
  file_id: string;
  db_id: string; // Original db_id from CSV, not MongoDB ObjectId
  first_name: string;
  middle_name?: string;
  last_name: string;
  dob: string;
  email: string;
  phone: string;
  path: string;
  waiver_date: string;
  reason?: string;
  error_message?: string;
  s3_url?: string;
  processed_at: string;
}

const CsvToObjectKeyMapDocumentLocker: Record<keyof ICsvDocumentLocker, string> = {
  file_id: "file_id",
  id: "db_id",
  first_name: "first_name",
  middle_name: "middle_name",
  last_name: "last_name",
  dob: "dob",
  email: "email",
  phone: "phone",
  path: "path",
  waiver_date: "waiver_date"
};

const logger = LoggerConfig('document-locker.migration');

/**
 * Create processed folder if it doesn't exist
 */
function ensureProcessedFolder(): string {
  const processedDir = path.join(process.cwd(), 'processed');
  if (!fs.existsSync(processedDir)) {
    fs.mkdirSync(processedDir, { recursive: true });
    logger.info(`Created processed directory: ${processedDir}`);
  }
  return processedDir;
}

/**
 * Create a processed record from CSV data
 */
function createProcessedRecord(
  csvData: ICsvDocumentLocker,
  rowNumber: number,
  reason?: string,
  errorMessage?: string,
  s3Url?: string
): ProcessedRecord {
  return {
    row_number: rowNumber,
    file_id: csvData.file_id || '',
    db_id: csvData.id || '', // Use original db_id from CSV
    first_name: csvData.first_name || '',
    middle_name: csvData.middle_name || '',
    last_name: csvData.last_name || '',
    dob: csvData.dob || '',
    email: csvData.email || '',
    phone: csvData.phone || '',
    path: csvData.path || '',
    waiver_date: csvData.waiver_date || '',
    reason: reason || '',
    error_message: errorMessage || '',
    s3_url: s3Url || '',
    processed_at: new Date().toISOString()
  };
}

/**
 * Write processed records to CSV file
 */
function writeProcessedRecordsToCSV(records: ProcessedRecord[], filePath: string, type: string): void {
  if (records.length === 0) {
    logger.info(`No ${type} records to write`);
    return;
  }

  const headers = [
    'row_number',
    'file_id',
    'db_id',
    'first_name',
    'middle_name',
    'last_name',
    'dob',
    'email',
    'phone',
    'waiver_date',
    'path',
    's3_url',
    'reason',
    'error_message',
    'processed_at'
  ];

  const csvContent = [
    headers.join(','),
    ...records.map(record => [
      record.row_number,
      `"${(record.file_id || '').replace(/"/g, '""')}"`,
      `"${(record.db_id || '').replace(/"/g, '""')}"`,
      `"${(record.first_name || '').replace(/"/g, '""')}"`,
      `"${(record.middle_name || '').replace(/"/g, '""')}"`,
      `"${(record.last_name || '').replace(/"/g, '""')}"`,
      `"${(record.dob || '').replace(/"/g, '""')}"`,
      `"${(record.email || '').replace(/"/g, '""')}"`,
      `"${(record.phone || '').replace(/"/g, '""')}"`,
      `"${(record.waiver_date || '').replace(/"/g, '""')}"`,
      `"${(record.path || '').replace(/"/g, '""')}"`,
      `"${(record.s3_url || '').replace(/"/g, '""')}"`,
      `"${(record.reason || '').replace(/"/g, '""')}"`,
      `"${(record.error_message || '').replace(/"/g, '""')}"`,
      `"${record.processed_at}"`
    ].join(','))
  ].join('\n');

  fs.writeFileSync(filePath, csvContent, 'utf8');
  logger.info(`Written ${records.length} ${type} records to: ${filePath}`);
}
/**
 * Validate document locker CSV data
 */
function validateDocumentLockerData(documentLocker: ICsvDocumentLocker, rowNumber: number): string[] {
  const errors: string[] = [];

  // if (!documentLocker.file_id || typeof documentLocker.file_id !== 'string' || !documentLocker.file_id.trim()) {
  //   errors.push(`Row ${rowNumber}: file_id is required`);
  // }

  if (!documentLocker.id || typeof documentLocker.id !== 'string' || !documentLocker.id.trim()) {
    errors.push(`Row ${rowNumber}: id is required`);
  }

  // if (!documentLocker.first_name || typeof documentLocker.first_name !== 'string' || !documentLocker.first_name.trim()) {
  //   errors.push(`Row ${rowNumber}: first_name is required`);
  // }

  // if (!documentLocker.last_name || typeof documentLocker.last_name !== 'string' || !documentLocker.last_name.trim()) {
  //   errors.push(`Row ${rowNumber}: last_name is required`);
  // }

  if (!documentLocker.path || typeof documentLocker.path !== 'string' || !documentLocker.path.trim()) {
    errors.push(`Row ${rowNumber}: path is required`);
  }

  return errors;
}

/**
 * Generate document name from file path
 */
function generateDocumentName(filePath: string, userId: string, firstName: string, lastName: string, middleName?: string): string {
  const fileName = path.basename(filePath);
  const nameWithoutExtension = path.parse(fileName).name;

  // If the filename already contains the name, use it as is
  if (nameWithoutExtension.includes(firstName) || nameWithoutExtension.includes(lastName)) {
    return nameWithoutExtension;
  }

  // Otherwise, create a name from the user details
  const fullName = middleName ? `${firstName} ${middleName} ${lastName}` : `${firstName} ${lastName}`;
  return `${fullName}-${userId}-${uuid4()}`;
}

/**
 * Generate S3 key for document
 */
function generateS3Key(userId: string, fileName: string): string {
  const fileExtension = path.extname(fileName);
  const baseName = path.basename(fileName, fileExtension);
  // return `waiver-${userId}-${baseName}-${uuid4()}-${fileExtension}`.replaceAll(' ', '-');
  return `document-locker/waiver-${userId}-${baseName}${fileExtension}`.trim().toLowerCase().replaceAll(' ', '-');
}

/**
 * Upload file to S3 and return the S3 URL
 */
async function uploadFileToS3(filePath: string, s3Key: string, s3Service: S3Service): Promise<string> {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    // Get file stats
    const stats = fs.statSync(filePath);
    logger.debug(`Uploading file: ${filePath} (${stats.size} bytes) to S3 key: ${s3Key}`);

    // Create read stream for the file
    const fileStream = fs.createReadStream(filePath);
    const contentType = mime.getType(filePath);
    // Upload to S3
    const res = await s3Service.uploadStream(s3Key, fileStream, undefined, contentType);

    // Return the S3 URL
    const s3Url = `${process.env.S3_ENDPOINT}/${s3Key}`;
    logger.debug(`Successfully uploaded file to S3: ${s3Url}`);

    return s3Url;
  } catch (error) {
    logger.error(`Failed to upload file ${filePath} to S3:`, error);
    throw error;
  }
}

/**
 * Process documents in batches with parallel S3 uploads
 */
async function processDocumentsBatch(
  documentBatch: Array<{ data: ICsvDocumentLocker, rowNumber: number, user: any, client: any }>,
  s3Service: S3Service
): Promise<{
  documentLockers: IDocumentLocker[],
  errors: string[],
  s3UploadsAttempted: number,
  s3UploadsSuccessful: number,
  s3UploadsFailed: number,
  successfulRecords: ProcessedRecord[],
  erroredRecords: ProcessedRecord[]
}> {
  const documentLockers: IDocumentLocker[] = [];
  const errors: string[] = [];
  const successfulRecords: ProcessedRecord[] = [];
  const erroredRecords: ProcessedRecord[] = [];
  let s3UploadsAttempted = 0;
  let s3UploadsSuccessful = 0;
  let s3UploadsFailed = 0;

  // Process uploads in parallel with concurrency limit
  const CONCURRENCY_LIMIT = 5; // Reduced for better stability

  for (let i = 0; i < documentBatch.length; i += CONCURRENCY_LIMIT) {
    const batch = documentBatch.slice(i, i + CONCURRENCY_LIMIT);

    const batchPromises = batch.map(async ({ data: csvDocumentLocker, rowNumber, user, client }) => {
      const startTime = Date.now();
      try {
        // Generate document name
        const documentName = generateDocumentName(
          csvDocumentLocker.path,
          user._id.toString(),
          csvDocumentLocker.first_name,
          csvDocumentLocker.last_name,
          csvDocumentLocker.middle_name
        );

        // Generate S3 key
        const s3Key = generateS3Key(user._id.toString(), csvDocumentLocker.path);

        // Upload file to S3 with retry logic
        let s3Url: string;
        let retryCount = 0;
        const maxRetries = 3;
        s3UploadsAttempted++;

        while (retryCount < maxRetries) {
          try {
            s3Url = await uploadFileToS3(csvDocumentLocker.path, s3Key, s3Service);
            s3UploadsSuccessful++;
            break;
          } catch (uploadError) {
            retryCount++;
            if (retryCount >= maxRetries) {
              s3UploadsFailed++;
              throw uploadError;
            }
            logger.warn(`Row ${rowNumber}: Upload attempt ${retryCount} failed, retrying... ${uploadError.message}`);
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
          }
        }

        // Create document locker entry
        const documentLocker = new DocumentLocker({
          userId: user._id,
          facilityId: client.facilityId,
          organizationId: global.config.organizationId,
          fileUrl: s3Url!,
          documentName: "Waiver",
          uploadedBy: global.config.organizationId,
          uploadedAt: new Date()
        });

        documentLockers.push(documentLocker);

        // Track successful record
        successfulRecords.push(createProcessedRecord(
          csvDocumentLocker,
          rowNumber,
          'Successfully processed and uploaded to S3',
          undefined,
          s3Url!
        ));

        const duration = Date.now() - startTime;
        logger.debug(`Row ${rowNumber}: Successfully processed document for user ${csvDocumentLocker.id} in ${duration}ms`);
      } catch (error) {
        const duration = Date.now() - startTime;
        const errorMsg = `Row ${rowNumber}: Failed to process document for user ${csvDocumentLocker.id} after ${duration}ms: ${error.message}`;
        errors.push(errorMsg);

        // Track errored record
        erroredRecords.push(createProcessedRecord(
          csvDocumentLocker,
          rowNumber,
          'Processing failed',
          error.message
        ));

        logger.error(errorMsg);
      }
    });

    // Wait for current batch to complete before starting next batch
    await Promise.all(batchPromises);

    // Log progress with timing
    const progress = Math.min(i + CONCURRENCY_LIMIT, documentBatch.length);
    logger.info(`Processed ${progress}/${documentBatch.length} documents in current batch`);
  }

  return { documentLockers, errors, s3UploadsAttempted, s3UploadsSuccessful, s3UploadsFailed, successfulRecords, erroredRecords };
}

/**
 * Configuration for batch processing
 */
interface BatchConfig {
  processingBatchSize: number;  // Number of documents to process in parallel
  dbBatchSize: number;         // Number of documents to save in one transaction
  maxRetries: number;          // Maximum retries for failed batches
  transactionTimeoutMs: number; // Transaction timeout in milliseconds
}

const DEFAULT_BATCH_CONFIG: BatchConfig = {
  processingBatchSize: 20,     // Process 20 documents at a time
  dbBatchSize: 50,            // Save 50 documents per transaction
  maxRetries: 3,              // Retry failed batches up to 3 times
  transactionTimeoutMs: 30000 // 30 second transaction timeout
};

/**
 * Save document lockers in batches with separate transactions
 */
async function saveDocumentLockersInBatches(
  documentLockers: IDocumentLocker[],
  config: BatchConfig = DEFAULT_BATCH_CONFIG
): Promise<{ saved: number, failed: number, errors: string[], failedBatches: number[] }> {
  const mongoose = await connectToMongo();
  const errors: string[] = [];
  const failedBatches: number[] = [];
  let totalSaved = 0;
  let totalFailed = 0;

  logger.info(`Saving ${documentLockers.length} document lockers in batches of ${config.dbBatchSize}`);

  const startTime = Date.now();
  const totalBatches = Math.ceil(documentLockers.length / config.dbBatchSize);

  for (let i = 0; i < documentLockers.length; i += config.dbBatchSize) {
    const batch = documentLockers.slice(i, i + config.dbBatchSize);
    const batchNumber = Math.floor(i / config.dbBatchSize) + 1;
    const batchStartTime = Date.now();

    logger.info(`Processing database batch ${batchNumber}/${totalBatches} (${batch.length} documents)`);

    let retryCount = 0;
    let batchSaved = false;

    while (retryCount <= config.maxRetries && !batchSaved) {
      const session = await mongoose.startSession();

      try {
        // Start transaction with timeout
        session.startTransaction({
          readConcern: { level: 'majority' },
          writeConcern: { w: 'majority' },
          maxTimeMS: config.transactionTimeoutMs
        });

        // Check for duplicates in this batch
        const existingDocumentLockers = await DocumentLocker.find({
          organizationId: global.config.organizationId,
          fileUrl: { $in: batch.map(dl => dl.fileUrl) }
        }).session(session).exec();

        let documentsToSave = batch;
        if (existingDocumentLockers.length > 0) {
          const existingUrls = new Set(existingDocumentLockers.map(dl => dl.fileUrl));
          documentsToSave = batch.filter(dl => !existingUrls.has(dl.fileUrl));

          if (documentsToSave.length < batch.length) {
            logger.info(`Batch ${batchNumber}: Filtered out ${batch.length - documentsToSave.length} duplicates`);
          }
        }

        if (documentsToSave.length > 0) {
          // Save documents in this batch
          const savedDocuments = await DocumentLocker.insertMany(documentsToSave, { session });
          await session.commitTransaction();

          totalSaved += savedDocuments.length;
          const batchDuration = Date.now() - batchStartTime;
          logger.info(`Batch ${batchNumber}: Successfully saved ${savedDocuments.length} documents in ${batchDuration}ms`);
        } else {
          await session.commitTransaction();
          const batchDuration = Date.now() - batchStartTime;
          logger.info(`Batch ${batchNumber}: No new documents to save (all duplicates) - completed in ${batchDuration}ms`);
        }

        batchSaved = true;

      } catch (error) {
        await session.abortTransaction();
        retryCount++;

        const errorMsg = `Batch ${batchNumber} attempt ${retryCount} failed: ${error.message}`;
        logger.error(errorMsg);

        if (retryCount <= config.maxRetries) {
          logger.info(`Retrying batch ${batchNumber} (attempt ${retryCount + 1}/${config.maxRetries + 1})`);
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1)));
        } else {
          const errorMsg = `Batch ${batchNumber}: Failed after ${config.maxRetries + 1} attempts - ${error.message}`;
          errors.push(errorMsg);
          failedBatches.push(batchNumber);
          totalFailed += batch.length;

          // Log individual document URLs that failed for debugging
          const failedUrls = batch.map(doc => doc.fileUrl).join(', ');
          logger.error(`Failed batch ${batchNumber} contained documents: ${failedUrls}`);
        }
      } finally {
        await session.endSession();
      }
    }
  }

  const totalDuration = Date.now() - startTime;
  logger.info(`Database batch processing completed in ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}s)`);

  return { saved: totalSaved, failed: totalFailed, errors, failedBatches };
}

const TEST_BATCH_CONFIG: BatchConfig = {
  processingBatchSize: 5,  // Process 5 documents at a time
  dbBatchSize: 10,         // Save 10 documents per transaction
  maxRetries: 2,           // Retry failed batches up to 2 times
  transactionTimeoutMs: 15000 // 15 second transaction timeout
}
/**
 * Migrate document locker data from CSV to MongoDB
 */
export async function migrateDocumentLocker(_dbName: string = 'hop-migration', batchConfig: Partial<BatchConfig> = TEST_BATCH_CONFIG): Promise<void> {
  const migrationStartTime = Date.now();
  logger.log('Starting document locker migration...');

  // Connect to database
  await connectToMongo();

  // Merge custom config with defaults
  const config: BatchConfig = { ...DEFAULT_BATCH_CONFIG, ...batchConfig };
  logger.info(`Using batch configuration:`, config);

  // Initialize comprehensive statistics
  const stats: MigrationStats = {
    totalRecordsInCSV: 0,
    validRecordsAfterValidation: 0,
    totalUsersInCSV: 0,
    usersFoundInDatabase: 0,
    usersWithClientRecords: 0,
    filesFoundOnDisk: 0,
    documentsReadyForProcessing: 0,
    s3UploadsAttempted: 0,
    s3UploadsSuccessful: 0,
    s3UploadsFailed: 0,
    mongoSaveAttempted: 0,
    mongoSaveSuccessful: 0,
    mongoSaveFailed: 0,
    totalSkipped: 0,
    validationErrors: 0
  };

  // Initialize tracking arrays for processed CSV files
  const validationErrorRecords: ProcessedRecord[] = [];
  const skippedRecords: ProcessedRecord[] = [];
  const erroredRecords: ProcessedRecord[] = [];
  const successfulRecords: ProcessedRecord[] = [];

  let uploadToS3: boolean = true
  logger.log('Starting document locker migration...');

  try {
    // Parse CSV file with row numbers
    const csvPath = path.resolve(process.env.DATA_FOLDER || path.join(process.cwd(), 'data'), 'waiver-scanned-with-db.csv');
    logger.info(`Reading CSV file from: ${csvPath}`);
    const documentLockerDataWithRows = await parseCSVWithRowNumbers<ICsvDocumentLocker>(csvPath, CsvToObjectKeyMapDocumentLocker);

    if (!documentLockerDataWithRows || documentLockerDataWithRows.length === 0) {
      logger.info('No document locker data found in CSV file');
      return;
    }

    stats.totalRecordsInCSV = documentLockerDataWithRows.length;
    logger.info(`Found ${documentLockerDataWithRows.length} document lockers in CSV file`);

    // Validate data and collect errors
    const validationErrors: string[] = [];
    const validDocumentLockers: Array<{ data: ICsvDocumentLocker, rowNumber: number }> = [];

    for (const { data: documentLocker, rowNumber } of documentLockerDataWithRows) {
      // Debug first few rows
      if (rowNumber <= 5) {
        logger.debug(`Row ${rowNumber} data:`, JSON.stringify(documentLocker, null, 2));
      }

      const errors = validateDocumentLockerData(documentLocker, rowNumber);
      if (errors.length > 0) {
        validationErrors.push(...errors);
        // Track validation error records
        validationErrorRecords.push(createProcessedRecord(
          documentLocker,
          rowNumber,
          'Validation failed',
          errors.join('; ')
        ));
      } else {
        validDocumentLockers.push({ data: documentLocker, rowNumber });
      }
    }

    stats.validRecordsAfterValidation = validDocumentLockers.length;
    stats.validationErrors = validationErrors.length;

    if (validationErrors.length > 0) {
      for (const error of validationErrors) {
        logger.warn(error);
      }
      logger.warn(`Found ${validationErrors.length} validation errors. Skipping invalid rows and continuing with valid data.`);
      logger.warn(`First 10 validation errors:\n${validationErrors.slice(0, 10).join('\n')}`);
    }

    if (validDocumentLockers.length === 0) {
      logger.error('No valid document locker records found after validation');
      return;
    }

    logger.info(`Processing ${validDocumentLockers.length} valid document locker records out of ${documentLockerDataWithRows.length} total records`);

    // Get all unique user IDs from CSV
    const userIds = [...new Set(validDocumentLockers.map(item => item.data.id).filter(id => id))];
    stats.totalUsersInCSV = userIds.length;

    // Fetch users from database (no session needed for read operations)
    const users = await User.find({
      organizationId: global.config.organizationId,
      id: { $in: userIds },
      role: global.roleMap.get(ENUM_ROLE_TYPE.USER)
    }).exec();

    stats.usersFoundInDatabase = users.length;

    // Create user ID to user document mapping
    const userIdMap = new Map<string, any>();
    users.forEach(user => {
      if (user.id) {
        userIdMap.set(user.id.toString(), user);
      }
    });

    // Get user ObjectIds for client lookup
    const userObjectIds = users.map(user => user._id);

    // Fetch clients to get facility information (no session needed for read operations)
    const clients = await Client.find({
      organizationId: global.config.organizationId,
      userId: { $in: userObjectIds }
    }).exec();

    stats.usersWithClientRecords = clients.length;

    // Create user ObjectId to client mapping
    const userToClientMap = new Map<string, any>();
    clients.forEach(client => {
      userToClientMap.set(client.userId.toString(), client);
    });

    // Process document lockers
    const documentLockers: IDocumentLocker[] = [];
    const skippedEntries: string[] = [];

    if (uploadToS3) {
      logger.info('S3 upload enabled - uploading files to S3');

      // https://fitness-saas-images.s3.ap-south-1.amazonaws.com/document-locker/passporta62ae916-6164-43cc-8e38-57790b3d5a6c-M.Sc.%20IV%20Sem%20%28Maths%29%20-%20Research%20Project%20Topics%20Distribution%20List%20%28Session%202024-25%29.pdf
      // Initialize S3 service
      const s3Service = new S3Service({
        region: process.env.AWS_S3_REGION || process.env.S3_REGION,
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        defaultBucket: process.env.AWS_S3_BUCKET
      });

      // Prepare documents for processing
      const documentsToProcess: Array<{ data: ICsvDocumentLocker, rowNumber: number, user: any, client: any }> = [];
      let filesFoundCount = 0;

      for (const { data: csvDocumentLocker, rowNumber } of validDocumentLockers) {
        const user = userIdMap.get(csvDocumentLocker.id);

        if (!user) {
          const reason = `User with id ${csvDocumentLocker.id} not found`;
          skippedEntries.push(`Row ${rowNumber}: ${reason}`);
          skippedRecords.push(createProcessedRecord(csvDocumentLocker, rowNumber, reason));
          continue;
        }

        const client = userToClientMap.get(user._id.toString());

        if (!client) {
          const reason = `Client record for user ${csvDocumentLocker.id} not found`;
          skippedEntries.push(`Row ${rowNumber}: ${reason}`);
          skippedRecords.push(createProcessedRecord(csvDocumentLocker, rowNumber, reason));
          continue;
        }

        // Check if file exists before adding to processing queue
        if (!fs.existsSync(csvDocumentLocker.path)) {
          const reason = `File not found: ${csvDocumentLocker.path}`;
          skippedEntries.push(`Row ${rowNumber}: ${reason}`);
          skippedRecords.push(createProcessedRecord(csvDocumentLocker, rowNumber, reason));
          continue;
        }

        filesFoundCount++;
        documentsToProcess.push({ data: csvDocumentLocker, rowNumber, user, client });
      }

      stats.filesFoundOnDisk = filesFoundCount;
      stats.documentsReadyForProcessing = documentsToProcess.length;

      // Remove test limit - process all documents
      // const TEST_LIMIT = 10;
      // if (documentsToProcess.length > TEST_LIMIT) {
      //   logger.info(`Limiting to first ${TEST_LIMIT} documents for S3 upload testing`);
      //   documentsToProcess.splice(TEST_LIMIT);
      // }

      logger.info(`Found ${documentsToProcess.length} documents to process with S3 upload`);

      // Process documents in batches with S3 upload
      const allErrors: string[] = [];

      for (let i = 0; i < documentsToProcess.length; i += config.processingBatchSize) {
        const batch = documentsToProcess.slice(i, i + config.processingBatchSize);
        logger.info(`Processing batch ${Math.floor(i / config.processingBatchSize) + 1}/${Math.ceil(documentsToProcess.length / config.processingBatchSize)} (${batch.length} documents)`);

        const {
          documentLockers: batchDocuments,
          errors,
          s3UploadsAttempted,
          s3UploadsSuccessful,
          s3UploadsFailed,
          successfulRecords: batchSuccessful,
          erroredRecords: batchErrored
        } = await processDocumentsBatch(batch, s3Service);

        documentLockers.push(...batchDocuments);
        allErrors.push(...errors);

        // Accumulate S3 statistics
        stats.s3UploadsAttempted += s3UploadsAttempted;
        stats.s3UploadsSuccessful += s3UploadsSuccessful;
        stats.s3UploadsFailed += s3UploadsFailed;

        // Collect processed records
        successfulRecords.push(...batchSuccessful);
        erroredRecords.push(...batchErrored);

        // Log batch completion
        logger.info(`Batch ${Math.floor(i / config.processingBatchSize) + 1} completed: ${batchDocuments.length} successful, ${errors.length} failed`);
      }

      skippedEntries.push(...allErrors);
    } else {
      logger.info('S3 upload disabled - using local file paths');

      // Process without S3 upload (original logic)
      let localProcessingCount = 0;
      let filesFoundCount = 0;

      for (const { data: csvDocumentLocker, rowNumber } of validDocumentLockers) {
        const user = userIdMap.get(csvDocumentLocker.id);

        if (!user) {
          const reason = `User with id ${csvDocumentLocker.id} not found`;
          skippedEntries.push(`Row ${rowNumber}: ${reason}`);
          skippedRecords.push(createProcessedRecord(csvDocumentLocker, rowNumber, reason));
          continue;
        }

        const client = userToClientMap.get(user._id.toString());

        if (!client) {
          const reason = `Client record for user ${csvDocumentLocker.id} not found`;
          skippedEntries.push(`Row ${rowNumber}: ${reason}`);
          skippedRecords.push(createProcessedRecord(csvDocumentLocker, rowNumber, reason));
          continue;
        }

        // Check if file exists (for statistics)
        if (fs.existsSync(csvDocumentLocker.path)) {
          filesFoundCount++;
        }

        // Generate document name (currently not used, but kept for future enhancement)
        // const documentName = generateDocumentName(
        //   csvDocumentLocker.path,
        //   user._id.toString(),
        //   csvDocumentLocker.first_name,
        //   csvDocumentLocker.last_name,
        //   csvDocumentLocker.middle_name
        // );

        const documentLocker = new DocumentLocker({
          userId: user._id,
          facilityId: client.facilityId,
          organizationId: global.config.organizationId,
          fileUrl: csvDocumentLocker.path, // Use local path
          documentName: "Waiver",
          uploadedBy: global.config.organizationId,
          updatedAt: new Date(csvDocumentLocker.waiver_date)
        });

        documentLockers.push(documentLocker);

        // Track successful record (local path processing)
        successfulRecords.push(createProcessedRecord(
          csvDocumentLocker,
          rowNumber,
          'Successfully processed with local path',
          undefined,
          csvDocumentLocker.path
        ));

        localProcessingCount++;
      }

      stats.filesFoundOnDisk = filesFoundCount;
      stats.documentsReadyForProcessing = localProcessingCount;
    }

    if (skippedEntries.length > 0) {
      for (const error of skippedEntries) {
        logger.warn(error);
      }
      logger.warn(`Skipped ${skippedEntries.length} entries during processing`);

    }

    if (documentLockers.length === 0) {
      logger.warn('No valid document lockers to save after processing');
      return;
    }

    logger.info(`Saving ${documentLockers.length} document lockers to database using batched transactions`);

    // Track MongoDB save statistics
    stats.mongoSaveAttempted = documentLockers.length;
    stats.totalSkipped = skippedEntries.length;

    // Save document lockers in batches with separate transactions
    const { saved, failed, errors, failedBatches } = await saveDocumentLockersInBatches(documentLockers, config);

    stats.mongoSaveSuccessful = saved;
    stats.mongoSaveFailed = failed;

    // Log final results
    logger.info(`Migration completed: ${saved} documents saved, ${failed} failed`);

    if (errors.length > 0) {
      logger.error(`Database save errors:\n${errors.join('\n')}`);

      if (failedBatches.length > 0) {
        logger.error(`Failed batch numbers: ${failedBatches.join(', ')}`);
        logger.info(`To retry failed batches, you may need to re-run the migration with adjusted batch sizes or investigate the specific errors above.`);
      }
    }

    // Calculate success rates
    const userMatchRate = stats.totalUsersInCSV > 0 ? ((stats.usersFoundInDatabase / stats.totalUsersInCSV) * 100).toFixed(2) : '0';
    const clientMatchRate = stats.usersFoundInDatabase > 0 ? ((stats.usersWithClientRecords / stats.usersFoundInDatabase) * 100).toFixed(2) : '0';
    const fileFoundRate = stats.validRecordsAfterValidation > 0 ? ((stats.filesFoundOnDisk / stats.validRecordsAfterValidation) * 100).toFixed(2) : '0';
    const s3SuccessRate = stats.s3UploadsAttempted > 0 ? ((stats.s3UploadsSuccessful / stats.s3UploadsAttempted) * 100).toFixed(2) : '0';
    const mongoSuccessRate = stats.mongoSaveAttempted > 0 ? ((stats.mongoSaveSuccessful / stats.mongoSaveAttempted) * 100).toFixed(2) : '0';
    const overallSuccessRate = stats.totalRecordsInCSV > 0 ? ((stats.mongoSaveSuccessful / stats.totalRecordsInCSV) * 100).toFixed(2) : '0';

    const totalMigrationTime = Date.now() - migrationStartTime;

    // Create processed CSV files
    const processedDir = ensureProcessedFolder();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];

    // Write separate CSV files for each category
    const validationErrorsFilePath = path.join(processedDir, `validation-errors-${timestamp}.csv`);
    const skippedFilePath = path.join(processedDir, `skipped-records-${timestamp}.csv`);
    const erroredFilePath = path.join(processedDir, `errored-records-${timestamp}.csv`);
    const successfulFilePath = path.join(processedDir, `successful-records-${timestamp}.csv`);

    writeProcessedRecordsToCSV(validationErrorRecords, validationErrorsFilePath, 'validation error');
    writeProcessedRecordsToCSV(skippedRecords, skippedFilePath, 'skipped');
    writeProcessedRecordsToCSV(erroredRecords, erroredFilePath, 'errored');
    writeProcessedRecordsToCSV(successfulRecords, successfulFilePath, 'successful');

    logger.info(`\n=== COMPREHENSIVE MIGRATION STATISTICS ===`);
    logger.info(`\n📊 CSV DATA ANALYSIS:`);
    logger.info(`- Total records in CSV: ${stats.totalRecordsInCSV}`);
    logger.info(`- Valid records after validation: ${stats.validRecordsAfterValidation}`);
    logger.info(`- Validation errors: ${stats.validationErrors}`);

    logger.info(`\n👥 USER MATCHING STATISTICS:`);
    logger.info(`- Total unique users in CSV: ${stats.totalUsersInCSV}`);
    logger.info(`- Users found in database: ${stats.usersFoundInDatabase} (${userMatchRate}%)`);
    logger.info(`- Users with client records: ${stats.usersWithClientRecords} (${clientMatchRate}% of found users)`);

    logger.info(`\n📁 FILE PROCESSING STATISTICS:`);
    logger.info(`- Files found on disk: ${stats.filesFoundOnDisk} (${fileFoundRate}% of valid records)`);
    logger.info(`- Documents ready for processing: ${stats.documentsReadyForProcessing}`);

    if (uploadToS3) {
      logger.info(`\n☁️ S3 UPLOAD STATISTICS:`);
      logger.info(`- S3 uploads attempted: ${stats.s3UploadsAttempted}`);
      logger.info(`- S3 uploads successful: ${stats.s3UploadsSuccessful} (${s3SuccessRate}%)`);
      logger.info(`- S3 uploads failed: ${stats.s3UploadsFailed}`);
    }

    logger.info(`\n🗄️ MONGODB SAVE STATISTICS:`);
    logger.info(`- MongoDB saves attempted: ${stats.mongoSaveAttempted}`);
    logger.info(`- MongoDB saves successful: ${stats.mongoSaveSuccessful} (${mongoSuccessRate}%)`);
    logger.info(`- MongoDB saves failed: ${stats.mongoSaveFailed}`);

    logger.info(`\n📈 OVERALL RESULTS:`);
    logger.info(`- Total records skipped: ${stats.totalSkipped}`);
    logger.info(`- Overall success rate: ${overallSuccessRate}% (${stats.mongoSaveSuccessful}/${stats.totalRecordsInCSV})`);
    logger.info(`- Total migration time: ${totalMigrationTime}ms (${(totalMigrationTime / 1000).toFixed(2)}s)`);

    logger.info(`\n📁 PROCESSED CSV FILES:`);
    logger.info(`- Validation errors: ${validationErrorRecords.length} records → ${validationErrorsFilePath}`);
    logger.info(`- Skipped records: ${skippedRecords.length} records → ${skippedFilePath}`);
    logger.info(`- Errored records: ${erroredRecords.length} records → ${erroredFilePath}`);
    logger.info(`- Successful records: ${successfulRecords.length} records → ${successfulFilePath}`);
    logger.info(`- All CSV files saved to: ${processedDir}`);

    logger.info(`\n=== END MIGRATION STATISTICS ===\n`);

  } catch (error) {
    logger.error('Error during document locker migration:', error);
    throw error;
  } finally {
    await closeConnection();
  }
};
